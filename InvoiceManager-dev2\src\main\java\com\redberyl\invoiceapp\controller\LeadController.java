package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.LeadDto;
import com.redberyl.invoiceapp.service.LeadService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/leads")
@Tag(name = "Lead", description = "Lead management API")
public class LeadController {

    @Autowired
    private LeadService leadService;

    @GetMapping
    @Operation(summary = "Get all leads", description = "Retrieve a list of all leads")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<LeadDto>> getAllLeads() {
        List<LeadDto> leads = leadService.getAllLeads();
        return new ResponseEntity<>(leads, HttpStatus.OK);
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get lead by ID", description = "Retrieve a lead by its ID")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<LeadDto> getLeadById(@PathVariable Long id) {
        LeadDto lead = leadService.getLeadById(id);
        return new ResponseEntity<>(lead, HttpStatus.OK);
    }

    @GetMapping("/status/{status}")
    @Operation(summary = "Get leads by status", description = "Retrieve all leads with a specific status")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<LeadDto>> getLeadsByStatus(@PathVariable String status) {
        List<LeadDto> leads = leadService.getLeadsByStatus(status);
        return new ResponseEntity<>(leads, HttpStatus.OK);
    }

    @GetMapping("/source/{source}")
    @Operation(summary = "Get leads by source", description = "Retrieve all leads from a specific source")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<LeadDto>> getLeadsBySource(@PathVariable String source) {
        List<LeadDto> leads = leadService.getLeadsBySource(source);
        return new ResponseEntity<>(leads, HttpStatus.OK);
    }

    @PostMapping
    @Operation(summary = "Create lead", description = "Create a new lead")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<LeadDto> createLead(@Valid @RequestBody LeadDto leadDto) {
        LeadDto createdLead = leadService.createLead(leadDto);
        return new ResponseEntity<>(createdLead, HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update lead", description = "Update an existing lead")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<LeadDto> updateLead(@PathVariable Long id, @Valid @RequestBody LeadDto leadDto) {
        LeadDto updatedLead = leadService.updateLead(id, leadDto);
        return new ResponseEntity<>(updatedLead, HttpStatus.OK);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete lead", description = "Delete a lead by its ID")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteLead(@PathVariable Long id) {
        leadService.deleteLead(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
