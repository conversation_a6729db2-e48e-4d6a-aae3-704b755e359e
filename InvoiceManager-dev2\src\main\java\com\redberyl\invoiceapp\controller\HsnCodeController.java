package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.HsnCodeDto;
import com.redberyl.invoiceapp.service.HsnCodeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/hsn-codes")
@Tag(name = "HSN Code", description = "HSN Code management API")
public class HsnCodeController {

    @Autowired
    private HsnCodeService hsnCodeService;

    @GetMapping
    @Operation(summary = "Get all HSN codes", description = "Retrieve a list of all HSN codes")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<HsnCodeDto>> getAllHsnCodes() {
        List<HsnCodeDto> hsnCodes = hsnCodeService.getAllHsnCodes();
        return new ResponseEntity<>(hsnCodes, HttpStatus.OK);
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get HSN code by ID", description = "Retrieve an HSN code by its ID")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<HsnCodeDto> getHsnCodeById(@PathVariable Long id) {
        HsnCodeDto hsnCode = hsnCodeService.getHsnCodeById(id);
        return new ResponseEntity<>(hsnCode, HttpStatus.OK);
    }

    @GetMapping("/code/{code}")
    @Operation(summary = "Get HSN code by code", description = "Retrieve an HSN code by its code")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<HsnCodeDto> getHsnCodeByCode(@PathVariable String code) {
        HsnCodeDto hsnCode = hsnCodeService.getHsnCodeByCode(code);
        return new ResponseEntity<>(hsnCode, HttpStatus.OK);
    }

    @PostMapping
    @Operation(summary = "Create HSN code", description = "Create a new HSN code")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<HsnCodeDto> createHsnCode(@Valid @RequestBody HsnCodeDto hsnCodeDto) {
        HsnCodeDto createdHsnCode = hsnCodeService.createHsnCode(hsnCodeDto);
        return new ResponseEntity<>(createdHsnCode, HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update HSN code", description = "Update an existing HSN code")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<HsnCodeDto> updateHsnCode(@PathVariable Long id, @Valid @RequestBody HsnCodeDto hsnCodeDto) {
        HsnCodeDto updatedHsnCode = hsnCodeService.updateHsnCode(id, hsnCodeDto);
        return new ResponseEntity<>(updatedHsnCode, HttpStatus.OK);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete HSN code", description = "Delete an HSN code by its ID")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteHsnCode(@PathVariable Long id) {
        hsnCodeService.deleteHsnCode(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
