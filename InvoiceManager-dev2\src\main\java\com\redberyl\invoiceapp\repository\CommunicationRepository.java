package com.redberyl.invoiceapp.repository;

import com.redberyl.invoiceapp.entity.Communication;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CommunicationRepository extends JpaRepository<Communication, Long> {
    List<Communication> findByClientId(Long clientId);
    List<Communication> findByLeadId(Long leadId);
    List<Communication> findByDealId(Long dealId);
}
