package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.StaffingTypeDto;
import com.redberyl.invoiceapp.service.StaffingTypeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/staffing-types")
@Tag(name = "Staffing Type", description = "Staffing Type management API")
public class StaffingTypeController {

    @Autowired
    private StaffingTypeService staffingTypeService;

    @GetMapping
    @Operation(summary = "Get all staffing types", description = "Retrieve a list of all staffing types")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<StaffingTypeDto>> getAllStaffingTypes() {
        List<StaffingTypeDto> staffingTypes = staffingTypeService.getAllStaffingTypes();
        return new ResponseEntity<>(staffingTypes, HttpStatus.OK);
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get staffing type by ID", description = "Retrieve a staffing type by its ID")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<StaffingTypeDto> getStaffingTypeById(@PathVariable Long id) {
        StaffingTypeDto staffingType = staffingTypeService.getStaffingTypeById(id);
        return new ResponseEntity<>(staffingType, HttpStatus.OK);
    }

    @GetMapping("/name/{name}")
    @Operation(summary = "Get staffing type by name", description = "Retrieve a staffing type by its name")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<StaffingTypeDto> getStaffingTypeByName(@PathVariable String name) {
        StaffingTypeDto staffingType = staffingTypeService.getStaffingTypeByName(name);
        return new ResponseEntity<>(staffingType, HttpStatus.OK);
    }

    @PostMapping
    @Operation(summary = "Create staffing type", description = "Create a new staffing type")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<StaffingTypeDto> createStaffingType(@Valid @RequestBody StaffingTypeDto staffingTypeDto) {
        StaffingTypeDto createdStaffingType = staffingTypeService.createStaffingType(staffingTypeDto);
        return new ResponseEntity<>(createdStaffingType, HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update staffing type", description = "Update an existing staffing type")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<StaffingTypeDto> updateStaffingType(@PathVariable Long id, @Valid @RequestBody StaffingTypeDto staffingTypeDto) {
        StaffingTypeDto updatedStaffingType = staffingTypeService.updateStaffingType(id, staffingTypeDto);
        return new ResponseEntity<>(updatedStaffingType, HttpStatus.OK);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete staffing type", description = "Delete a staffing type by its ID")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteStaffingType(@PathVariable Long id) {
        staffingTypeService.deleteStaffingType(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
