package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.DocumentTemplateVersionDto;
import com.redberyl.invoiceapp.service.DocumentTemplateVersionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/document-template-versions")
@Tag(name = "Document Template Version", description = "Document Template Version management API")
public class DocumentTemplateVersionController {

        @Autowired
        private DocumentTemplateVersionService documentTemplateVersionService;

        @GetMapping
        @Operation(summary = "Get all document template versions", description = "Retrieve a list of all document template versions")
        // @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
        public ResponseEntity<List<DocumentTemplateVersionDto>> getAllDocumentTemplateVersions() {
                List<DocumentTemplateVersionDto> documentTemplateVersions = documentTemplateVersionService
                                .getAllDocumentTemplateVersions();
                return new ResponseEntity<>(documentTemplateVersions, HttpStatus.OK);
        }

        @GetMapping("/{id}")
        @Operation(summary = "Get document template version by ID", description = "Retrieve a document template version by its ID")
        @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
        public ResponseEntity<DocumentTemplateVersionDto> getDocumentTemplateVersionById(@PathVariable Long id) {
                DocumentTemplateVersionDto documentTemplateVersion = documentTemplateVersionService
                                .getDocumentTemplateVersionById(id);
                return new ResponseEntity<>(documentTemplateVersion, HttpStatus.OK);
        }

        @GetMapping("/template/{templateId}")
        @Operation(summary = "Get document template versions by template ID", description = "Retrieve all document template versions for a specific template")
        @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
        public ResponseEntity<List<DocumentTemplateVersionDto>> getDocumentTemplateVersionsByTemplateId(
                        @PathVariable Long templateId) {
                List<DocumentTemplateVersionDto> documentTemplateVersions = documentTemplateVersionService
                                .getDocumentTemplateVersionsByTemplateId(templateId);
                return new ResponseEntity<>(documentTemplateVersions, HttpStatus.OK);
        }

        @GetMapping("/template/{templateId}/version/{versionNumber}")
        @Operation(summary = "Get document template version by template ID and version number", description = "Retrieve a document template version by its template ID and version number")
        @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
        public ResponseEntity<DocumentTemplateVersionDto> getDocumentTemplateVersionByTemplateIdAndVersionNumber(
                        @PathVariable Long templateId, @PathVariable Integer versionNumber) {
                return documentTemplateVersionService
                                .getDocumentTemplateVersionByTemplateIdAndVersionNumber(templateId, versionNumber)
                                .map(version -> new ResponseEntity<>(version, HttpStatus.OK))
                                .orElse(new ResponseEntity<>(HttpStatus.NOT_FOUND));
        }

        @GetMapping("/template/{templateId}/active")
        @Operation(summary = "Get active document template versions by template ID", description = "Retrieve all active document template versions for a specific template")
        @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
        public ResponseEntity<List<DocumentTemplateVersionDto>> getActiveDocumentTemplateVersionsByTemplateId(
                        @PathVariable Long templateId) {
                List<DocumentTemplateVersionDto> documentTemplateVersions = documentTemplateVersionService
                                .getActiveDocumentTemplateVersionsByTemplateId(templateId);
                return new ResponseEntity<>(documentTemplateVersions, HttpStatus.OK);
        }

        @PostMapping
        @Operation(summary = "Create document template version", description = "Create a new document template version")
        // @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
        public ResponseEntity<DocumentTemplateVersionDto> createDocumentTemplateVersion(
                        @Valid @RequestBody DocumentTemplateVersionDto documentTemplateVersionDto) {
                DocumentTemplateVersionDto createdDocumentTemplateVersion = documentTemplateVersionService
                                .createDocumentTemplateVersion(documentTemplateVersionDto);
                return new ResponseEntity<>(createdDocumentTemplateVersion, HttpStatus.CREATED);
        }

        @PutMapping("/{id}")
        @Operation(summary = "Update document template version", description = "Update an existing document template version")
        @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
        public ResponseEntity<DocumentTemplateVersionDto> updateDocumentTemplateVersion(@PathVariable Long id,
                        @Valid @RequestBody DocumentTemplateVersionDto documentTemplateVersionDto) {
                DocumentTemplateVersionDto updatedDocumentTemplateVersion = documentTemplateVersionService
                                .updateDocumentTemplateVersion(id, documentTemplateVersionDto);
                return new ResponseEntity<>(updatedDocumentTemplateVersion, HttpStatus.OK);
        }

        @PutMapping("/{id}/activate")
        @Operation(summary = "Activate document template version", description = "Activate a document template version")
        @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
        public ResponseEntity<DocumentTemplateVersionDto> activateDocumentTemplateVersion(@PathVariable Long id) {
                DocumentTemplateVersionDto activatedDocumentTemplateVersion = documentTemplateVersionService
                                .activateDocumentTemplateVersion(id);
                return new ResponseEntity<>(activatedDocumentTemplateVersion, HttpStatus.OK);
        }

        @PutMapping("/{id}/deactivate")
        @Operation(summary = "Deactivate document template version", description = "Deactivate a document template version")
        @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
        public ResponseEntity<DocumentTemplateVersionDto> deactivateDocumentTemplateVersion(@PathVariable Long id) {
                DocumentTemplateVersionDto deactivatedDocumentTemplateVersion = documentTemplateVersionService
                                .deactivateDocumentTemplateVersion(id);
                return new ResponseEntity<>(deactivatedDocumentTemplateVersion, HttpStatus.OK);
        }

        @DeleteMapping("/{id}")
        @Operation(summary = "Delete document template version", description = "Delete a document template version by its ID")
        @PreAuthorize("hasRole('ADMIN')")
        public ResponseEntity<Void> deleteDocumentTemplateVersion(@PathVariable Long id) {
                documentTemplateVersionService.deleteDocumentTemplateVersion(id);
                return new ResponseEntity<>(HttpStatus.NO_CONTENT);
        }
}
