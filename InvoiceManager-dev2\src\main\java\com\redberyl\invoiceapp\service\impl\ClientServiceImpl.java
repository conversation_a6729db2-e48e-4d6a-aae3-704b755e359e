package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.ClientDto;
import com.redberyl.invoiceapp.entity.Client;
import com.redberyl.invoiceapp.repository.ClientRepository;
import com.redberyl.invoiceapp.service.ClientService;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class ClientServiceImpl implements ClientService {

    @Autowired
    private ClientRepository clientRepository;

    @Override
    public List<ClientDto> getAllClients() {
        return clientRepository.findAll().stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public ClientDto getClientById(Long id) {
        Client client = clientRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Client not found with id: " + id));
        return convertToDto(client);
    }

    @Override
    @Transactional
    public ClientDto createClient(ClientDto clientDto) {
        Client client = convertToEntity(clientDto);
        Client savedClient = clientRepository.save(client);
        return convertToDto(savedClient);
    }

    @Override
    @Transactional
    public ClientDto updateClient(Long id, ClientDto clientDto) {
        Client existingClient = clientRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Client not found with id: " + id));
        
        existingClient.setName(clientDto.getName());
        
        Client updatedClient = clientRepository.save(existingClient);
        return convertToDto(updatedClient);
    }

    @Override
    @Transactional
    public void deleteClient(Long id) {
        if (!clientRepository.existsById(id)) {
            throw new EntityNotFoundException("Client not found with id: " + id);
        }
        clientRepository.deleteById(id);
    }

    private ClientDto convertToDto(Client client) {
        ClientDto dto = ClientDto.builder()
                .id(client.getId())
                .name(client.getName())
                .build();
        
        // Set the audit fields from BaseEntity
        dto.setCreatedAt(client.getCreatedAt());
        dto.setUpdatedAt(client.getModifiedAt());
        
        return dto;
    }

    private Client convertToEntity(ClientDto clientDto) {
        Client client = Client.builder()
                .name(clientDto.getName())
                .build();
        
        if (clientDto.getId() != null) {
            client.setId(clientDto.getId());
        }
        
        return client;
    }
}

