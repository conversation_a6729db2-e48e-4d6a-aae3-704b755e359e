package com.redberyl.invoiceapp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = false)
public class TaxRateDto extends BaseDto {

    @Schema(description = "Tax Rate ID - automatically generated", accessMode = Schema.AccessMode.READ_ONLY)
    private Long id;

    @Schema(description = "Tax Type ID - reference to an existing Tax Type", example = "1", required = true)
    @NotNull(message = "Tax type ID is required")
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Long taxTypeId;

    @Schema(description = "Tax Type details", accessMode = Schema.AccessMode.READ_ONLY)
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private TaxTypeDto taxType;

    @Schema(description = "Tax rate value", example = "18.0", required = true)
    @NotNull(message = "Rate is required")
    @Positive(message = "Rate must be positive")
    private BigDecimal rate;

    @Schema(description = "Date from which the tax rate is effective", example = "2025-04-17", required = true)
    @NotNull(message = "Effective from date is required")
    private LocalDate effectiveFrom;

    @Schema(description = "Date until which the tax rate is effective", example = "2025-06-22")
    private LocalDate effectiveTo;
}
