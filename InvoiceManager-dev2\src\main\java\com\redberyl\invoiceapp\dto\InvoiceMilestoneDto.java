package com.redberyl.invoiceapp.dto;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
public class InvoiceMilestoneDto extends BaseDto {
    private Long id;
    
    @NotNull(message = "Invoice ID is required")
    private Long invoiceId;
    
    // Remove circular reference to avoid compilation issues
    // private InvoiceDto invoice;
    
    private String description;
    
    @Positive(message = "Amount must be positive")
    private BigDecimal amount;
    
    private LocalDate milestoneDate;
}
