package com.redberyl.invoiceapp.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
public class InvoiceTypeDto extends BaseDto {
    private Long id;
    
    @NotBlank(message = "Invoice type is required")
    private String invoiceType;
    
    private String typeDesc;
}
