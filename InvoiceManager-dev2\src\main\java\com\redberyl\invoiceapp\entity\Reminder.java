package com.redberyl.invoiceapp.entity;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Table(name = "reminders")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Reminder extends BaseEntity {

    @Id
    @SequenceGenerator(name = "reminder_seq", sequenceName = "reminder_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "reminder_seq")
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "invoice_id")
    private Invoice invoice;

    @Column(name = "method")
    private String method;

    @Column(name = "status")
    private String status = "sent";

    @Column(name = "note")
    private String note;
}
