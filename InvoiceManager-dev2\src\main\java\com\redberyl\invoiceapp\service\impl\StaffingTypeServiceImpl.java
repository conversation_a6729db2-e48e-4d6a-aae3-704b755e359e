package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.StaffingTypeDto;
import com.redberyl.invoiceapp.entity.StaffingType;
import com.redberyl.invoiceapp.repository.StaffingTypeRepository;
import com.redberyl.invoiceapp.service.StaffingTypeService;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class StaffingTypeServiceImpl implements StaffingTypeService {

    @Autowired
    private StaffingTypeRepository staffingTypeRepository;

    @Override
    public List<StaffingTypeDto> getAllStaffingTypes() {
        return staffingTypeRepository.findAll().stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public StaffingTypeDto getStaffingTypeById(Long id) {
        StaffingType staffingType = staffingTypeRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Staffing Type not found with id: " + id));
        return convertToDto(staffingType);
    }

    @Override
    public StaffingTypeDto getStaffingTypeByName(String name) {
        StaffingType staffingType = staffingTypeRepository.findByName(name)
                .orElseThrow(() -> new EntityNotFoundException("Staffing Type not found with name: " + name));
        return convertToDto(staffingType);
    }

    @Override
    @Transactional
    public StaffingTypeDto createStaffingType(StaffingTypeDto staffingTypeDto) {
        StaffingType staffingType = convertToEntity(staffingTypeDto);
        StaffingType savedStaffingType = staffingTypeRepository.save(staffingType);
        return convertToDto(savedStaffingType);
    }

    @Override
    @Transactional
    public StaffingTypeDto updateStaffingType(Long id, StaffingTypeDto staffingTypeDto) {
        StaffingType existingStaffingType = staffingTypeRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Staffing Type not found with id: " + id));
        
        existingStaffingType.setName(staffingTypeDto.getName());
        
        StaffingType updatedStaffingType = staffingTypeRepository.save(existingStaffingType);
        return convertToDto(updatedStaffingType);
    }

    @Override
    @Transactional
    public void deleteStaffingType(Long id) {
        if (!staffingTypeRepository.existsById(id)) {
            throw new EntityNotFoundException("Staffing Type not found with id: " + id);
        }
        staffingTypeRepository.deleteById(id);
    }

    private StaffingTypeDto convertToDto(StaffingType staffingType) {
        return StaffingTypeDto.builder()
                .id(staffingType.getId())
                .name(staffingType.getName())
                .build();
    }

    private StaffingType convertToEntity(StaffingTypeDto staffingTypeDto) {
        return StaffingType.builder()
                .id(staffingTypeDto.getId())
                .name(staffingTypeDto.getName())
                .build();
    }
}
