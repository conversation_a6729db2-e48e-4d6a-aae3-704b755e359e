package com.redberyl.invoiceapp.config;

import com.redberyl.invoiceapp.entity.auth.ERole;
import com.redberyl.invoiceapp.entity.auth.Role;
import com.redberyl.invoiceapp.entity.auth.User;
import com.redberyl.invoiceapp.repository.RoleRepository;
import com.redberyl.invoiceapp.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.util.HashSet;
import java.util.Set;

@Configuration
public class DataInitializer {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Bean
    public CommandLineRunner initData() {
        return args -> {
            // Check if admin user already exists
            if (userRepository.findByUsername("admin").isEmpty()) {
                // Create admin user
                User adminUser = new User();
                adminUser.setUsername("admin");
                adminUser.setEmail("<EMAIL>");
                adminUser.setPassword(passwordEncoder.encode("admin123"));

                Set<Role> roles = new HashSet<>();
                Role adminRole = roleRepository.findByName(ERole.ROLE_ADMIN)
                        .orElseThrow(() -> new RuntimeException("Error: Role ADMIN is not found."));
                roles.add(adminRole);
                adminUser.setRoles(roles);

                userRepository.save(adminUser);
                System.out.println("Admin user created successfully!");
            }
        };
    }
}
