package com.redberyl.invoiceapp.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.examples.Example;
import io.swagger.v3.oas.models.media.Content;
import io.swagger.v3.oas.models.media.MediaType;
import io.swagger.v3.oas.models.media.Schema;
import io.swagger.v3.oas.models.parameters.RequestBody;
import org.springdoc.core.customizers.OpenApiCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SwaggerExampleConfig {

    @Bean
    public OpenApiCustomizer taxRateExampleCustomizer() {
        return openApi -> {
            // Create a simple example for tax rate creation
            Example taxRateExample = new Example();
            taxRateExample.setValue("{\n" +
                    "  \"taxTypeId\": 1,\n" +
                    "  \"rate\": 18.0,\n" +
                    "  \"effectiveFrom\": \"2025-04-17\",\n" +
                    "  \"effectiveTo\": \"2025-06-22\"\n" +
                    "}");
            taxRateExample.setSummary("Simple Tax Rate Example");
            taxRateExample.setDescription("Example of creating a tax rate with only the required fields");

            // Find the POST operation for tax rates
            openApi.getPaths().forEach((path, pathItem) -> {
                if (path.contains("/tax-rates") && pathItem.getPost() != null) {
                    RequestBody requestBody = pathItem.getPost().getRequestBody();
                    if (requestBody != null && requestBody.getContent() != null) {
                        Content content = requestBody.getContent();
                        MediaType mediaType = content.get("application/json");
                        if (mediaType != null) {
                            // Add our example
                            mediaType.addExamples("simpleExample", taxRateExample);
                            
                            // Set it as the default example
                            Schema schema = mediaType.getSchema();
                            if (schema != null) {
                                schema.setExample("{\n" +
                                        "  \"taxTypeId\": 1,\n" +
                                        "  \"rate\": 18.0,\n" +
                                        "  \"effectiveFrom\": \"2025-04-17\",\n" +
                                        "  \"effectiveTo\": \"2025-06-22\"\n" +
                                        "}");
                            }
                        }
                    }
                }
            });
        };
    }
}
