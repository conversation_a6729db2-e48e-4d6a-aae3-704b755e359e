package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.RedberylAccountDto;
import com.redberyl.invoiceapp.service.RedberylAccountService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/redberyl-accounts")
@Tag(name = "Redberyl Account", description = "Redberyl Account management API")
public class RedberylAccountController {

    @Autowired
    private RedberylAccountService redberylAccountService;

    @GetMapping
    @Operation(summary = "Get all Redberyl accounts", description = "Retrieve a list of all Redberyl accounts")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<RedberylAccountDto>> getAllRedberylAccounts() {
        List<RedberylAccountDto> redberylAccounts = redberylAccountService.getAllRedberylAccounts();
        return new ResponseEntity<>(redberylAccounts, HttpStatus.OK);
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get Redberyl account by ID", description = "Retrieve a Redberyl account by its ID")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<RedberylAccountDto> getRedberylAccountById(@PathVariable Long id) {
        RedberylAccountDto redberylAccount = redberylAccountService.getRedberylAccountById(id);
        return new ResponseEntity<>(redberylAccount, HttpStatus.OK);
    }

    @GetMapping("/account-no/{accountNo}")
    @Operation(summary = "Get Redberyl account by account number", description = "Retrieve a Redberyl account by its account number")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<RedberylAccountDto> getRedberylAccountByAccountNo(@PathVariable String accountNo) {
        RedberylAccountDto redberylAccount = redberylAccountService.getRedberylAccountByAccountNo(accountNo);
        return new ResponseEntity<>(redberylAccount, HttpStatus.OK);
    }

    @GetMapping("/gstn/{gstn}")
    @Operation(summary = "Get Redberyl account by GSTN", description = "Retrieve a Redberyl account by its GSTN")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<RedberylAccountDto> getRedberylAccountByGstn(@PathVariable String gstn) {
        RedberylAccountDto redberylAccount = redberylAccountService.getRedberylAccountByGstn(gstn);
        return new ResponseEntity<>(redberylAccount, HttpStatus.OK);
    }

    @GetMapping("/pan/{panNo}")
    @Operation(summary = "Get Redberyl account by PAN", description = "Retrieve a Redberyl account by its PAN number")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<RedberylAccountDto> getRedberylAccountByPanNo(@PathVariable String panNo) {
        RedberylAccountDto redberylAccount = redberylAccountService.getRedberylAccountByPanNo(panNo);
        return new ResponseEntity<>(redberylAccount, HttpStatus.OK);
    }

    @PostMapping
    @Operation(summary = "Create Redberyl account", description = "Create a new Redberyl account")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<RedberylAccountDto> createRedberylAccount(@Valid @RequestBody RedberylAccountDto redberylAccountDto) {
        RedberylAccountDto createdRedberylAccount = redberylAccountService.createRedberylAccount(redberylAccountDto);
        return new ResponseEntity<>(createdRedberylAccount, HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update Redberyl account", description = "Update an existing Redberyl account")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<RedberylAccountDto> updateRedberylAccount(@PathVariable Long id, @Valid @RequestBody RedberylAccountDto redberylAccountDto) {
        RedberylAccountDto updatedRedberylAccount = redberylAccountService.updateRedberylAccount(id, redberylAccountDto);
        return new ResponseEntity<>(updatedRedberylAccount, HttpStatus.OK);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete Redberyl account", description = "Delete a Redberyl account by its ID")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteRedberylAccount(@PathVariable Long id) {
        redberylAccountService.deleteRedberylAccount(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
