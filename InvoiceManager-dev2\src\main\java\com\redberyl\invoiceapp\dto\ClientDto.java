package com.redberyl.invoiceapp.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.Set;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
public class ClientDto extends BaseDto {
    private Long id;

    @NotBlank(message = "Client name is required")
    private String name;

    // Remove circular references to avoid compilation issues
    // @Builder.Default
    // private Set<ProjectDto> projects = new HashSet<>();
    //
    // @Builder.Default
    // private Set<InvoiceDto> invoices = new HashSet<>();
}
