package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.BdmDto;
import com.redberyl.invoiceapp.dto.BdmPaymentDto;
import com.redberyl.invoiceapp.dto.InvoiceDto;
import com.redberyl.invoiceapp.entity.Bdm;
import com.redberyl.invoiceapp.entity.BdmPayment;
import com.redberyl.invoiceapp.entity.Invoice;
import com.redberyl.invoiceapp.exception.ResourceNotFoundException;
import com.redberyl.invoiceapp.repository.BdmPaymentRepository;
import com.redberyl.invoiceapp.repository.BdmRepository;
import com.redberyl.invoiceapp.repository.InvoiceRepository;
import com.redberyl.invoiceapp.service.BdmPaymentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class BdmPaymentServiceImpl implements BdmPaymentService {

    @Autowired
    private BdmPaymentRepository bdmPaymentRepository;

    @Autowired
    private BdmRepository bdmRepository;

    @Autowired
    private InvoiceRepository invoiceRepository;

    @Override
    public List<BdmPaymentDto> getAllBdmPayments() {
        List<BdmPayment> bdmPayments = bdmPaymentRepository.findAll();
        return bdmPayments.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public BdmPaymentDto getBdmPaymentById(Long id) {
        BdmPayment bdmPayment = bdmPaymentRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("BdmPayment", "id", id));
        return convertToDto(bdmPayment);
    }

    @Override
    public List<BdmPaymentDto> getBdmPaymentsByBdmId(Long bdmId) {
        List<BdmPayment> bdmPayments = bdmPaymentRepository.findByBdmId(bdmId);
        return bdmPayments.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<BdmPaymentDto> getBdmPaymentsByInvoiceId(Long invoiceId) {
        List<BdmPayment> bdmPayments = bdmPaymentRepository.findByInvoiceId(invoiceId);
        return bdmPayments.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public BdmPaymentDto createBdmPayment(BdmPaymentDto bdmPaymentDto) {
        BdmPayment bdmPayment = convertToEntity(bdmPaymentDto);
        BdmPayment savedBdmPayment = bdmPaymentRepository.save(bdmPayment);
        return convertToDto(savedBdmPayment);
    }

    @Override
    @Transactional
    public BdmPaymentDto updateBdmPayment(Long id, BdmPaymentDto bdmPaymentDto) {
        BdmPayment existingBdmPayment = bdmPaymentRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("BdmPayment", "id", id));

        // Update fields
        if (bdmPaymentDto.getBdmId() != null) {
            Bdm bdm = bdmRepository.findById(bdmPaymentDto.getBdmId())
                    .orElseThrow(() -> new ResourceNotFoundException("Bdm", "id", bdmPaymentDto.getBdmId()));
            existingBdmPayment.setBdm(bdm);
        }

        if (bdmPaymentDto.getInvoiceId() != null) {
            Invoice invoice = invoiceRepository.findById(bdmPaymentDto.getInvoiceId())
                    .orElseThrow(() -> new ResourceNotFoundException("Invoice", "id", bdmPaymentDto.getInvoiceId()));
            existingBdmPayment.setInvoice(invoice);
        }

        if (bdmPaymentDto.getAmount() != null) {
            existingBdmPayment.setAmount(bdmPaymentDto.getAmount());
        }

        if (bdmPaymentDto.getPaidOn() != null) {
            existingBdmPayment.setPaidOn(bdmPaymentDto.getPaidOn());
        }

        if (bdmPaymentDto.getIsPaid() != null) {
            existingBdmPayment.setIsPaid(bdmPaymentDto.getIsPaid());
        }

        BdmPayment updatedBdmPayment = bdmPaymentRepository.save(existingBdmPayment);
        return convertToDto(updatedBdmPayment);
    }

    @Override
    @Transactional
    public void deleteBdmPayment(Long id) {
        if (!bdmPaymentRepository.existsById(id)) {
            throw new ResourceNotFoundException("BdmPayment", "id", id);
        }
        bdmPaymentRepository.deleteById(id);
    }

    private BdmPaymentDto convertToDto(BdmPayment bdmPayment) {
        BdmPaymentDto dto = BdmPaymentDto.builder()
                .id(bdmPayment.getId())
                .amount(bdmPayment.getAmount())
                .paidOn(bdmPayment.getPaidOn())
                .isPaid(bdmPayment.getIsPaid())
                .build();

        // Set the invoice ID and BDM ID
        if (bdmPayment.getInvoice() != null) {
            dto.setInvoiceId(bdmPayment.getInvoice().getId());

            // Set the invoice object
            InvoiceDto invoiceDto = InvoiceDto.builder()
                    .id(bdmPayment.getInvoice().getId())
                    .invoiceNumber(bdmPayment.getInvoice().getInvoiceNumber())
                    .billingAmount(bdmPayment.getInvoice().getBillingAmount())
                    .taxAmount(bdmPayment.getInvoice().getTaxAmount())
                    .totalAmount(bdmPayment.getInvoice().getTotalAmount())
                    .invoiceDate(bdmPayment.getInvoice().getInvoiceDate())
                    .dueDate(bdmPayment.getInvoice().getDueDate())
                    .build();
            // Commented out to avoid circular reference
            // dto.setInvoice(invoiceDto);
        }

        if (bdmPayment.getBdm() != null) {
            dto.setBdmId(bdmPayment.getBdm().getId());

            // Set the BDM object
            BdmDto bdmDto = BdmDto.builder()
                    .id(bdmPayment.getBdm().getId())
                    .name(bdmPayment.getBdm().getName())
                    .email(bdmPayment.getBdm().getEmail())
                    .phone(bdmPayment.getBdm().getPhone())
                    .gstNumber(bdmPayment.getBdm().getGstNumber())
                    .billingAddress(bdmPayment.getBdm().getBillingAddress())
                    .build();
            dto.setBdm(bdmDto);
        }

        // Set the audit fields from BaseEntity
        dto.setCreatedAt(bdmPayment.getCreatedAt());
        dto.setUpdatedAt(bdmPayment.getModifiedAt());

        return dto;
    }

    private BdmPayment convertToEntity(BdmPaymentDto dto) {
        BdmPayment bdmPayment = BdmPayment.builder()
                .amount(dto.getAmount())
                .paidOn(dto.getPaidOn())
                .isPaid(dto.getIsPaid())
                .build();

        // Set the ID if it's an update operation
        if (dto.getId() != null) {
            bdmPayment.setId(dto.getId());
        }

        // Set the invoice and BDM
        if (dto.getInvoiceId() != null) {
            Invoice invoice = invoiceRepository.findById(dto.getInvoiceId())
                    .orElseThrow(() -> new ResourceNotFoundException("Invoice", "id", dto.getInvoiceId()));
            bdmPayment.setInvoice(invoice);
        }

        if (dto.getBdmId() != null) {
            Bdm bdm = bdmRepository.findById(dto.getBdmId())
                    .orElseThrow(() -> new ResourceNotFoundException("Bdm", "id", dto.getBdmId()));
            bdmPayment.setBdm(bdm);
        }

        return bdmPayment;
    }
}

