package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.InvoiceMilestoneDto;
import com.redberyl.invoiceapp.entity.Invoice;
import com.redberyl.invoiceapp.entity.InvoiceMilestone;
import com.redberyl.invoiceapp.repository.InvoiceMilestoneRepository;
import com.redberyl.invoiceapp.repository.InvoiceRepository;
import com.redberyl.invoiceapp.service.InvoiceMilestoneService;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class InvoiceMilestoneServiceImpl implements InvoiceMilestoneService {

    @Autowired
    private InvoiceMilestoneRepository invoiceMilestoneRepository;

    @Autowired
    private InvoiceRepository invoiceRepository;

    @Override
    public List<InvoiceMilestoneDto> getAllInvoiceMilestones() {
        return invoiceMilestoneRepository.findAll().stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public InvoiceMilestoneDto getInvoiceMilestoneById(Long id) {
        InvoiceMilestone invoiceMilestone = invoiceMilestoneRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Invoice Milestone not found with id: " + id));
        return convertToDto(invoiceMilestone);
    }

    @Override
    public List<InvoiceMilestoneDto> getInvoiceMilestonesByInvoiceId(Long invoiceId) {
        return invoiceMilestoneRepository.findByInvoiceId(invoiceId).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<InvoiceMilestoneDto> getInvoiceMilestonesByDateBefore(LocalDate date) {
        return invoiceMilestoneRepository.findByMilestoneDateBefore(date).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public InvoiceMilestoneDto createInvoiceMilestone(InvoiceMilestoneDto invoiceMilestoneDto) {
        InvoiceMilestone invoiceMilestone = convertToEntity(invoiceMilestoneDto);
        InvoiceMilestone savedInvoiceMilestone = invoiceMilestoneRepository.save(invoiceMilestone);
        return convertToDto(savedInvoiceMilestone);
    }

    @Override
    @Transactional
    public InvoiceMilestoneDto updateInvoiceMilestone(Long id, InvoiceMilestoneDto invoiceMilestoneDto) {
        InvoiceMilestone existingInvoiceMilestone = invoiceMilestoneRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Invoice Milestone not found with id: " + id));
        
        existingInvoiceMilestone.setDescription(invoiceMilestoneDto.getDescription());
        existingInvoiceMilestone.setAmount(invoiceMilestoneDto.getAmount());
        existingInvoiceMilestone.setMilestoneDate(invoiceMilestoneDto.getMilestoneDate());
        
        InvoiceMilestone updatedInvoiceMilestone = invoiceMilestoneRepository.save(existingInvoiceMilestone);
        return convertToDto(updatedInvoiceMilestone);
    }

    @Override
    @Transactional
    public void deleteInvoiceMilestone(Long id) {
        if (!invoiceMilestoneRepository.existsById(id)) {
            throw new EntityNotFoundException("Invoice Milestone not found with id: " + id);
        }
        invoiceMilestoneRepository.deleteById(id);
    }

    private InvoiceMilestoneDto convertToDto(InvoiceMilestone invoiceMilestone) {
        return InvoiceMilestoneDto.builder()
                .id(invoiceMilestone.getId())
                .invoiceId(invoiceMilestone.getInvoice().getId())
                .description(invoiceMilestone.getDescription())
                .amount(invoiceMilestone.getAmount())
                .milestoneDate(invoiceMilestone.getMilestoneDate())
                .build();
    }

    private InvoiceMilestone convertToEntity(InvoiceMilestoneDto invoiceMilestoneDto) {
        InvoiceMilestone invoiceMilestone = new InvoiceMilestone();
        invoiceMilestone.setId(invoiceMilestoneDto.getId());
        
        if (invoiceMilestoneDto.getInvoiceId() != null) {
            Invoice invoice = invoiceRepository.findById(invoiceMilestoneDto.getInvoiceId())
                    .orElseThrow(() -> new EntityNotFoundException("Invoice not found with id: " + invoiceMilestoneDto.getInvoiceId()));
            invoiceMilestone.setInvoice(invoice);
        }
        
        invoiceMilestone.setDescription(invoiceMilestoneDto.getDescription());
        invoiceMilestone.setAmount(invoiceMilestoneDto.getAmount());
        invoiceMilestone.setMilestoneDate(invoiceMilestoneDto.getMilestoneDate());
        
        return invoiceMilestone;
    }
}
