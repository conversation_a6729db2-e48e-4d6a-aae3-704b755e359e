package com.redberyl.invoiceapp.service;

import com.redberyl.invoiceapp.dto.InvoiceMilestoneDto;

import java.time.LocalDate;
import java.util.List;

public interface InvoiceMilestoneService {
    List<InvoiceMilestoneDto> getAllInvoiceMilestones();
    InvoiceMilestoneDto getInvoiceMilestoneById(Long id);
    List<InvoiceMilestoneDto> getInvoiceMilestonesByInvoiceId(Long invoiceId);
    List<InvoiceMilestoneDto> getInvoiceMilestonesByDateBefore(LocalDate date);
    InvoiceMilestoneDto createInvoiceMilestone(InvoiceMilestoneDto invoiceMilestoneDto);
    InvoiceMilestoneDto updateInvoiceMilestone(Long id, InvoiceMilestoneDto invoiceMilestoneDto);
    void deleteInvoiceMilestone(Long id);
}
