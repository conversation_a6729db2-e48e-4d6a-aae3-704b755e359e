package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.LeadDto;
import com.redberyl.invoiceapp.entity.Lead;
import com.redberyl.invoiceapp.exception.ResourceNotFoundException;
import com.redberyl.invoiceapp.repository.LeadRepository;
import com.redberyl.invoiceapp.service.LeadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class LeadServiceImpl implements LeadService {

    @Autowired
    private LeadRepository leadRepository;

    @Override
    public List<LeadDto> getAllLeads() {
        List<Lead> leads = leadRepository.findAll();
        return leads.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public LeadDto getLeadById(Long id) {
        Lead lead = leadRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Lead", "id", id));
        return convertToDto(lead);
    }

    @Override
    public List<LeadDto> getLeadsByStatus(String status) {
        List<Lead> leads = leadRepository.findByStatus(status);
        return leads.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<LeadDto> getLeadsBySource(String source) {
        List<Lead> leads = leadRepository.findBySource(source);
        return leads.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public LeadDto createLead(LeadDto leadDto) {
        Lead lead = convertToEntity(leadDto);
        Lead savedLead = leadRepository.save(lead);
        return convertToDto(savedLead);
    }

    @Override
    @Transactional
    public LeadDto updateLead(Long id, LeadDto leadDto) {
        Lead existingLead = leadRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Lead", "id", id));

        existingLead.setName(leadDto.getName());
        existingLead.setEmail(leadDto.getEmail());
        existingLead.setPhone(leadDto.getPhone());
        existingLead.setStatus(leadDto.getStatus());
        existingLead.setSource(leadDto.getSource());

        Lead updatedLead = leadRepository.save(existingLead);
        return convertToDto(updatedLead);
    }

    @Override
    @Transactional
    public void deleteLead(Long id) {
        if (!leadRepository.existsById(id)) {
            throw new ResourceNotFoundException("Lead", "id", id);
        }
        leadRepository.deleteById(id);
    }

    private LeadDto convertToDto(Lead lead) {
        LeadDto dto = LeadDto.builder()
                .id(lead.getId())
                .name(lead.getName())
                .email(lead.getEmail())
                .phone(lead.getPhone())
                .status(lead.getStatus())
                .source(lead.getSource())
                .build();

        // Set the audit fields
        dto.setCreatedAt(lead.getCreatedAt());
        dto.setUpdatedAt(lead.getModifiedAt());

        return dto;
    }

    private Lead convertToEntity(LeadDto dto) {
        Lead lead = new Lead();
        lead.setId(dto.getId());
        lead.setName(dto.getName());
        lead.setEmail(dto.getEmail());
        lead.setPhone(dto.getPhone());
        lead.setStatus(dto.getStatus());
        lead.setSource(dto.getSource());

        return lead;
    }
}
