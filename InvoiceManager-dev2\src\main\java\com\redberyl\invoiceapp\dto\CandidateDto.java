package com.redberyl.invoiceapp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
public class CandidateDto extends BaseDto {
    private Long id;
    
    @NotNull(message = "Client ID is required")
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Long clientId;
    
    // Include the full client object
    private ClientDto client;
    
    @NotNull(message = "Project ID is required")
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Long projectId;
    
    // Include the full project object
    private ProjectDto project;
    
    @NotBlank(message = "Candidate name is required")
    private String name;
    
    private LocalDateTime joiningDate;
    private BigDecimal billingRate;
    private String designation;
    private String panNo;
    private String aadharNo;
    private String uanNo;
    private BigDecimal experienceInYrs;
    private String bankAccountNo;
    private String branchName;
    private String ifscCode;
    private String address;
    private BigDecimal salaryOffered;
    
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Long managerSpocId;
    
    // Include the full manager SPOC object
    private SpocDto managerSpoc;
    
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Long accountHeadSpocId;
    
    // Include the full account head SPOC object
    private SpocDto accountHeadSpoc;
    
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Long businessHeadSpocId;
    
    // Include the full business head SPOC object
    private SpocDto businessHeadSpoc;
    
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Long hrSpocId;
    
    // Include the full HR SPOC object
    private SpocDto hrSpoc;
    
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Long financeSpocId;
    
    // Include the full finance SPOC object
    private SpocDto financeSpoc;
}
