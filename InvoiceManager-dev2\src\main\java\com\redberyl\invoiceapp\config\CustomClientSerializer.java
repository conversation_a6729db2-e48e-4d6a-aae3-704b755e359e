package com.redberyl.invoiceapp.config;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.redberyl.invoiceapp.entity.Client;
import org.springframework.boot.jackson.JsonComponent;

import java.io.IOException;

@JsonComponent
public class CustomClientSerializer extends JsonSerializer<Client> {

    @Override
    public void serialize(Client client, JsonGenerator jsonGenerator, SerializerProvider serializerProvider)
            throws IOException {
        jsonGenerator.writeStartObject();

        // Write the client properties
        jsonGenerator.writeNumberField("id", client.getId());
        jsonGenerator.writeStringField("name", client.getName());

        // Write the audit fields
        if (client.getCreatedAt() != null) {
            jsonGenerator.writeStringField("created_at", client.getCreatedAt().toString());
        }

        if (client.getModifiedAt() != null) {
            jsonGenerator.writeStringField("updated_at", client.getModifiedAt().toString());
        }

        jsonGenerator.writeEndObject();
    }
}
