<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Redberyl Invoice CRM API Documentation</title>
    <link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/4.18.3/swagger-ui.min.css" />
    <link rel="icon" type="image/png" href="https://redberyl.com/favicon.ico" />
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;
        }
        .swagger-ui .topbar {
            background-color: #2c3e50;
            padding: 10px 0;
        }
        .swagger-ui .topbar .wrapper {
            display: flex;
            align-items: center;
            max-width: 1460px;
            margin: 0 auto;
            padding: 0 20px;
        }
        .swagger-ui .topbar .download-url-wrapper {
            display: flex;
            align-items: center;
            flex: 1;
            justify-content: flex-end;
        }
        .swagger-ui .topbar a {
            color: white;
            text-decoration: none;
            font-weight: bold;
            margin-right: 15px;
        }
        .swagger-ui .topbar img {
            height: 40px;
            margin-right: 10px;
        }
        .swagger-ui .topbar .title {
            color: white;
            font-size: 1.5em;
            font-weight: bold;
        }
        .swagger-ui .info {
            margin: 20px 0;
        }
        .swagger-ui .info .title {
            font-size: 36px;
            color: #3b4151;
        }
        .swagger-ui .info .description {
            font-size: 16px;
            color: #3b4151;
        }
        .swagger-ui .scheme-container {
            background-color: #f7f7f7;
            box-shadow: 0 1px 2px 0 rgba(0,0,0,.15);
        }
        .swagger-ui .btn.authorize {
            background-color: #4990e2;
            color: white;
            border-color: #4990e2;
        }
        .swagger-ui .btn.authorize svg {
            fill: white;
        }
        .swagger-ui .opblock-tag {
            font-size: 18px;
            font-weight: bold;
            color: #3b4151;
        }
        .swagger-ui .opblock {
            margin-bottom: 15px;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
        }
        .swagger-ui .opblock .opblock-summary {
            padding: 10px;
        }
        .swagger-ui .opblock .opblock-summary-method {
            font-weight: bold;
            border-radius: 3px;
            padding: 6px 12px;
        }
        .swagger-ui .opblock-tag-section {
            margin-bottom: 30px;
        }
        .custom-footer {
            text-align: center;
            padding: 20px;
            background-color: #f7f7f7;
            margin-top: 50px;
            border-top: 1px solid #e0e0e0;
        }
        .custom-footer a {
            color: #4990e2;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div id="swagger-ui"></div>

    <div class="custom-footer">
        <p>© 2025 Redberyl. All rights reserved.</p>
        <p>
            <a href="https://redberyl.com" target="_blank">Website</a> |
            <a href="https://redberyl.com/docs" target="_blank">Documentation</a> |
            <a href="https://redberyl.com/contact" target="_blank">Contact</a>
        </p>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/4.18.3/swagger-ui-bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/4.18.3/swagger-ui-standalone-preset.min.js"></script>
    <script>
        window.onload = function() {
            const ui = SwaggerUIBundle({
                url: "/api/api-docs",
                dom_id: '#swagger-ui',
                deepLinking: true,
                presets: [
                    SwaggerUIBundle.presets.apis,
                    SwaggerUIStandalonePreset
                ],
                plugins: [
                    SwaggerUIBundle.plugins.DownloadUrl
                ],
                layout: "BaseLayout",
                docExpansion: "list",
                defaultModelsExpandDepth: -1,
                displayRequestDuration: true,
                filter: true,
                syntaxHighlight: {
                    activated: true,
                    theme: "monokai"
                },
                tryItOutEnabled: true,
                persistAuthorization: true,
                tagsSorter: "alpha",
                operationsSorter: "alpha",
                defaultModelRendering: "model",
                showExtensions: true,
                showCommonExtensions: true
            });

            // Add custom logo and title to the topbar
            const topbar = document.querySelector('.swagger-ui .topbar');
            if (topbar) {
                const wrapper = topbar.querySelector('.wrapper');
                if (wrapper) {
                    wrapper.innerHTML = `
                        <div style="display: flex; align-items: center;">
                            <img src="https://redberyl.com/logo.png" alt="Redberyl Logo" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIGZpbGw9IiM0OTkwZTIiLz48cGF0aCBkPSJNMTAgMTBIMzBWMzBIMTBWMTBaIiBmaWxsPSJ3aGl0ZSIvPjwvc3ZnPg==';" />
                            <span class="title">Redberyl Invoice CRM API</span>
                        </div>
                        <div class="download-url-wrapper">
                            <a href="https://redberyl.com/docs" target="_blank">Documentation</a>
                            <a href="https://redberyl.com/contact" target="_blank">Support</a>
                        </div>
                    `;
                }
            }
        }
    </script>
</body>
</html>
