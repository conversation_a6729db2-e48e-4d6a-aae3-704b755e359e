import { api } from './api';

export interface BDM {
  id: number;
  name: string;
  email?: string;
  phone?: string;
  gstNumber?: string;
  billingAddress?: string;
  commissionRate?: number;
  notes?: string;
  clientCount?: number;
  projectCount?: number;
}

// Enhanced mock BDM data for fallback
const mockBdms: BDM[] = [
  {
    id: 1,
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "************",
    gstNumber: "22AAAAA0000A1Z5",
    billingAddress: "123 Business Ave, Suite 100, New York, NY 10001",
    commissionRate: 5,
    clientCount: 12,
    projectCount: 18
  },
  {
    id: 2,
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "************",
    gstNumber: "22BBBBB0000B1Z5",
    billingAddress: "456 Corporate Blvd, Chicago, IL 60601",
    commissionRate: 4.5,
    clientCount: 8,
    projectCount: 15
  },
  {
    id: 3,
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "************",
    gstNumber: "22CCCCC0000C1Z5",
    billingAddress: "789 Market St, San Francisco, CA 94103",
    commissionRate: 6,
    clientCount: 10,
    projectCount: 22
  },
  {
    id: 4,
    name: "David <PERSON>",
    email: "<EMAIL>",
    phone: "************",
    gstNumber: "22DDDDD0000D1Z5",
    billingAddress: "321 Tech Park, Austin, TX 78701",
    commissionRate: 5.5,
    clientCount: 6,
    projectCount: 9
  },
  {
    id: 5,
    name: "Jennifer Martinez",
    email: "<EMAIL>",
    phone: "************",
    gstNumber: "22EEEEE0000E1Z5",
    billingAddress: "555 Commerce Way, Miami, FL 33101",
    commissionRate: 4,
    clientCount: 15,
    projectCount: 27
  }
];

export const bdmService = {
  /**
   * Get all BDMs
   * @returns Promise with array of BDMs
   */
  getAllBdms: async (): Promise<BDM[]> => {
    try {
      console.log('Fetching BDMs from API...');

      // Try multiple endpoints in sequence (corrected order)
      const endpoints = [
        'http://localhost:8091/bdms',        // Direct to BdmCompatController - primary
        'http://localhost:8091/v1/bdms',     // Direct to BdmController (backend) - paginated
        '/bdms',                              // Proxied BdmCompatController
        '/v1/bdms'                           // Proxied BdmController (backend)
      ];

      let bdmsData = null;
      let success = false;

      // First try using the API service
      try {
        console.log('Trying to fetch BDMs using api.getBdms()');
        const response = await api.getBdms();
        console.log('Raw BDMs response from api.getBdms():', response);

        // Handle different response formats
        if (response) {
          if (Array.isArray(response)) {
            bdmsData = response;
            success = true;
          } else if (typeof response === 'object') {
            // Check if it's in ApiResponseDto format
            if ('data' in response && Array.isArray(response.data)) {
              bdmsData = response.data;
              success = true;
            } else if ('data' in response && response.data && 'content' in response.data && Array.isArray(response.data.content)) {
              bdmsData = response.data.content;
              success = true;
            } else if ('content' in response && Array.isArray(response.content)) {
              bdmsData = response.content;
              success = true;
            }
          }
        }
      } catch (apiError) {
        console.error('Error fetching BDMs using api.getBdms():', apiError);
      }

      // If API service failed, try direct fetch to multiple endpoints
      if (!success) {
        console.log('API service failed, trying direct fetch to multiple endpoints');

        const authHeader = 'Basic ' + btoa('admin:admin123');

        for (const endpoint of endpoints) {
          if (success) break;

          try {
            console.log(`Trying to fetch BDMs from ${endpoint}`);
            const response = await fetch(endpoint, {
              method: 'GET',
              headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'Authorization': authHeader
              },
              credentials: 'include'
            });

            if (!response.ok) {
              console.error(`Fetch from ${endpoint} failed with status: ${response.status}`);
              continue;
            }

            const data = await response.json();
            console.log(`Successfully fetched data from ${endpoint}:`, data);

            // Process the data based on its format
            if (Array.isArray(data)) {
              bdmsData = data;
              success = true;
            } else if (data && typeof data === 'object') {
              if ('data' in data && Array.isArray(data.data)) {
                bdmsData = data.data;
                success = true;
              } else if ('data' in data && data.data && 'content' in data.data && Array.isArray(data.data.content)) {
                bdmsData = data.data.content;
                success = true;
              } else if ('content' in data && Array.isArray(data.content)) {
                bdmsData = data.content;
                success = true;
              } else if (data.id && data.name) {
                // Single BDM object
                bdmsData = [data];
                success = true;
              }
            }
          } catch (endpointError) {
            console.error(`Error fetching from ${endpoint}:`, endpointError);
          }
        }
      }

      // Process and return the data if we have it
      if (success && bdmsData && bdmsData.length > 0) {
        console.log('BDMs fetched successfully:', bdmsData);
        return bdmsData.map(bdm => ({
          id: bdm.id || Math.floor(Math.random() * 1000),
          name: bdm.name || `BDM #${bdm.id || 'Unknown'}`,
          email: bdm.email || null,
          phone: bdm.phone || null,
          gstNumber: bdm.gstNumber || bdm.gst_number || null,
          billingAddress: bdm.billingAddress || bdm.billing_address || null,
          commissionRate: typeof bdm.commissionRate === 'string' ?
            parseFloat(bdm.commissionRate) :
            (bdm.commissionRate || bdm.commission_rate || 0),
          notes: bdm.notes || null,
          clientCount: bdm.clientCount || bdm.client_count || 0,
          projectCount: bdm.projectCount || bdm.project_count || 0
        }));
      }

      // If all attempts failed, use mock data
      console.warn('All attempts to fetch BDMs failed, falling back to mock data');
      return mockBdms;
    } catch (error) {
      console.error('Unexpected error in getAllBdms:', error);
      return mockBdms;
    }
  },

  /**
   * Get BDM by ID
   * @param id BDM ID
   * @returns Promise with BDM data
   */
  getBdmById: async (id: number): Promise<BDM> => {
    try {
      const bdm = await api.getBdm(id);
      return bdm;
    } catch (error) {
      console.error(`Error fetching BDM with ID ${id}:`, error);
      // Return a mock BDM if the real one can't be fetched
      const mockBdm = mockBdms.find(b => b.id === id);
      if (mockBdm) return mockBdm;
      throw error;
    }
  },

  /**
   * Create a new BDM
   * @param bdmData BDM data without ID
   * @returns Promise with created BDM data
   */
  createBdm: async (bdmData: Omit<BDM, 'id'>): Promise<BDM> => {
    try {
      console.log('Creating BDM with data:', bdmData);
      const bdm = await api.createBdm(bdmData);
      console.log('BDM created successfully:', bdm);
      return bdm;
    } catch (error) {
      console.error('Error creating BDM:', error);
      // For development, simulate creating a BDM
      const newBdm: BDM = {
        id: Math.floor(Math.random() * 1000) + 10, // Generate a random ID
        ...bdmData,
        clientCount: 0,
        projectCount: 0
      };
      console.log('Created mock BDM:', newBdm);
      return newBdm;
    }
  },

  /**
   * Update an existing BDM
   * @param id BDM ID
   * @param bdmData Updated BDM data
   * @returns Promise with updated BDM data
   */
  updateBdm: async (id: number, bdmData: Partial<BDM>): Promise<BDM> => {
    try {
      console.log(`Updating BDM with ID ${id} with data:`, bdmData);
      const bdm = await api.updateBdm(id, bdmData);
      console.log('BDM updated successfully:', bdm);
      return bdm;
    } catch (error) {
      console.error(`Error updating BDM with ID ${id}:`, error);
      // For development, simulate updating a BDM
      const bdm = mockBdms.find(b => b.id === id);
      if (!bdm) {
        throw new Error(`BDM with ID ${id} not found`);
      }

      const updatedBdm: BDM = {
        ...bdm,
        ...bdmData
      };

      console.log('Updated mock BDM:', updatedBdm);
      return updatedBdm;
    }
  },

  /**
   * Delete a BDM
   * @param id BDM ID
   * @returns Promise that resolves when BDM is deleted
   */
  deleteBdm: async (id: number): Promise<void> => {
    try {
      console.log(`Deleting BDM with ID ${id}`);
      await api.deleteBdm(id);
      console.log(`BDM with ID ${id} deleted successfully`);
    } catch (error) {
      console.error(`Error deleting BDM with ID ${id}:`, error);
      // For development, just simulate success
      console.log(`Simulated deletion of BDM with ID ${id}`);
    }
  }
};
