package com.redberyl.invoiceapp.entity;

import jakarta.persistence.*;
import lombok.*;

import java.util.HashSet;
import java.util.Set;

@Entity
@Table(name = "tax_types")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TaxType extends BaseEntity {

    @Id
    @SequenceGenerator(name = "tax_type_seq", sequenceName = "tax_type_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "tax_type_seq")
    private Long id;

    @Column(name = "tax_type")
    private String taxType;

    @Column(name = "tax_type_description")
    private String taxTypeDescription;

    @Builder.Default
    @OneToMany(mappedBy = "taxType", cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<TaxRate> taxRates = new HashSet<>();

    // Helper method to manage bidirectional relationship
    public void addTaxRate(TaxRate taxRate) {
        taxRates.add(taxRate);
        taxRate.setTaxType(this);
    }

    // Helper method to remove a tax rate
    public void removeTaxRate(TaxRate taxRate) {
        taxRates.remove(taxRate);
        taxRate.setTaxType(null);
    }
}
