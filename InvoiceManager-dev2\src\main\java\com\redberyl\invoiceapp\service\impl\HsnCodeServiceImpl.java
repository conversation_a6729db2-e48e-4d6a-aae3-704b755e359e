package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.HsnCodeDto;
import com.redberyl.invoiceapp.entity.HsnCode;
import com.redberyl.invoiceapp.repository.HsnCodeRepository;
import com.redberyl.invoiceapp.service.HsnCodeService;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class HsnCodeServiceImpl implements HsnCodeService {

    @Autowired
    private HsnCodeRepository hsnCodeRepository;

    @Override
    public List<HsnCodeDto> getAllHsnCodes() {
        return hsnCodeRepository.findAll().stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public HsnCodeDto getHsnCodeById(Long id) {
        HsnCode hsnCode = hsnCodeRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("HSN Code not found with id: " + id));
        return convertToDto(hsnCode);
    }

    @Override
    public HsnCodeDto getHsnCodeByCode(String code) {
        HsnCode hsnCode = hsnCodeRepository.findByCode(code)
                .orElseThrow(() -> new EntityNotFoundException("HSN Code not found with code: " + code));
        return convertToDto(hsnCode);
    }

    @Override
    @Transactional
    public HsnCodeDto createHsnCode(HsnCodeDto hsnCodeDto) {
        HsnCode hsnCode = convertToEntity(hsnCodeDto);
        HsnCode savedHsnCode = hsnCodeRepository.save(hsnCode);
        return convertToDto(savedHsnCode);
    }

    @Override
    @Transactional
    public HsnCodeDto updateHsnCode(Long id, HsnCodeDto hsnCodeDto) {
        HsnCode existingHsnCode = hsnCodeRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("HSN Code not found with id: " + id));
        
        existingHsnCode.setCode(hsnCodeDto.getCode());
        existingHsnCode.setDescription(hsnCodeDto.getDescription());
        existingHsnCode.setGstRate(hsnCodeDto.getGstRate());
        
        HsnCode updatedHsnCode = hsnCodeRepository.save(existingHsnCode);
        return convertToDto(updatedHsnCode);
    }

    @Override
    @Transactional
    public void deleteHsnCode(Long id) {
        if (!hsnCodeRepository.existsById(id)) {
            throw new EntityNotFoundException("HSN Code not found with id: " + id);
        }
        hsnCodeRepository.deleteById(id);
    }

    private HsnCodeDto convertToDto(HsnCode hsnCode) {
        return HsnCodeDto.builder()
                .id(hsnCode.getId())
                .code(hsnCode.getCode())
                .description(hsnCode.getDescription())
                .gstRate(hsnCode.getGstRate())
                .build();
    }

    private HsnCode convertToEntity(HsnCodeDto hsnCodeDto) {
        return HsnCode.builder()
                .id(hsnCodeDto.getId())
                .code(hsnCodeDto.getCode())
                .description(hsnCodeDto.getDescription())
                .gstRate(hsnCodeDto.getGstRate())
                .build();
    }
}
