package com.redberyl.invoiceapp.service;

import com.redberyl.invoiceapp.dto.TaxRateDto;

import java.time.LocalDate;
import java.util.List;

public interface TaxRateService {
    List<TaxRateDto> getAllTaxRates();
    TaxRateDto getTaxRateById(Long id);
    List<TaxRateDto> getTaxRatesByTaxTypeId(Long taxTypeId);
    List<TaxRateDto> getTaxRatesEffectiveOnDate(LocalDate date);
    TaxRateDto createTaxRate(TaxRateDto taxRateDto);
    TaxRateDto updateTaxRate(Long id, TaxRateDto taxRateDto);
    void deleteTaxRate(Long id);
}
