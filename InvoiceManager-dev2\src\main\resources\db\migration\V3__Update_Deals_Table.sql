-- First, drop any dependent tables that reference the deals table
ALTER TABLE communications DROP CONSTRAINT IF EXISTS communications_deal_id_fkey;
ALTER TABLE generated_documents DROP CONSTRAINT IF EXISTS generated_documents_deal_id_fkey;

-- Drop the existing deals table
DROP TABLE IF EXISTS deals;

-- Create the new deals table with the correct format
CREATE TABLE deals (
    id SERIAL PRIMARY KEY,
    lead_id INT REFERENCES leads(id) ON DELETE CASCADE,
    client_id INT REFERENCES clients(id),
    project_name VARCHAR(255),
    value_estimate NUMERIC(12,2),
    expected_closure_date DATE,
    status VARCHAR(50) DEFAULT 'open',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Recreate the foreign key constraints for dependent tables
ALTER TABLE communications 
    ADD CONSTRAINT communications_deal_id_fkey 
    FOREIGN KEY (deal_id) REFERENCES deals(id);

ALTER TABLE generated_documents 
    ADD CONSTRAINT generated_documents_deal_id_fkey 
    FOREIGN KEY (deal_id) REFERENCES deals(id);
