package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.DocumentTemplateDto;
import com.redberyl.invoiceapp.service.DocumentTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/document-templates")
@Tag(name = "Document Template", description = "Document Template management API")
public class DocumentTemplateController {

    @Autowired
    private DocumentTemplateService documentTemplateService;

    @GetMapping
    @Operation(summary = "Get all document templates", description = "Retrieve a list of all document templates")
    public ResponseEntity<List<DocumentTemplateDto>> getAllDocumentTemplates() {
        List<DocumentTemplateDto> documentTemplates = documentTemplateService.getAllDocumentTemplates();
        return new ResponseEntity<>(documentTemplates, HttpStatus.OK);
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get document template by ID", description = "Retrieve a document template by its ID")
    public ResponseEntity<DocumentTemplateDto> getDocumentTemplateById(@PathVariable Long id) {
        DocumentTemplateDto documentTemplate = documentTemplateService.getDocumentTemplateById(id);
        return new ResponseEntity<>(documentTemplate, HttpStatus.OK);
    }

    @GetMapping("/type/{templateType}")
    @Operation(summary = "Get document templates by type", description = "Retrieve all document templates of a specific type")
    public ResponseEntity<List<DocumentTemplateDto>> getDocumentTemplatesByType(@PathVariable String templateType) {
        List<DocumentTemplateDto> documentTemplates = documentTemplateService.getDocumentTemplatesByType(templateType);
        return new ResponseEntity<>(documentTemplates, HttpStatus.OK);
    }

    @PostMapping
    @Operation(summary = "Create document template", description = "Create a new document template")
    public ResponseEntity<DocumentTemplateDto> createDocumentTemplate(
            @Valid @RequestBody DocumentTemplateDto documentTemplateDto) {
        DocumentTemplateDto createdDocumentTemplate = documentTemplateService
                .createDocumentTemplate(documentTemplateDto);
        return new ResponseEntity<>(createdDocumentTemplate, HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update document template", description = "Update an existing document template")
    public ResponseEntity<DocumentTemplateDto> updateDocumentTemplate(@PathVariable Long id,
            @Valid @RequestBody DocumentTemplateDto documentTemplateDto) {
        DocumentTemplateDto updatedDocumentTemplate = documentTemplateService.updateDocumentTemplate(id,
                documentTemplateDto);
        return new ResponseEntity<>(updatedDocumentTemplate, HttpStatus.OK);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete document template", description = "Delete a document template by its ID")
    public ResponseEntity<Void> deleteDocumentTemplate(@PathVariable Long id) {
        documentTemplateService.deleteDocumentTemplate(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
