package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.GeneratedDocumentDto;
import com.redberyl.invoiceapp.entity.Client;
import com.redberyl.invoiceapp.entity.Deal;
import com.redberyl.invoiceapp.entity.DocumentTemplate;
import com.redberyl.invoiceapp.entity.DocumentTemplateVersion;
import com.redberyl.invoiceapp.entity.GeneratedDocument;
import com.redberyl.invoiceapp.repository.ClientRepository;
import com.redberyl.invoiceapp.repository.DealRepository;
import com.redberyl.invoiceapp.repository.DocumentTemplateRepository;
import com.redberyl.invoiceapp.repository.DocumentTemplateVersionRepository;
import com.redberyl.invoiceapp.repository.GeneratedDocumentRepository;
import com.redberyl.invoiceapp.service.GeneratedDocumentService;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class GeneratedDocumentServiceImpl implements GeneratedDocumentService {

    @Autowired
    private GeneratedDocumentRepository generatedDocumentRepository;

    @Autowired
    private DocumentTemplateRepository documentTemplateRepository;

    @Autowired
    private DocumentTemplateVersionRepository documentTemplateVersionRepository;

    @Autowired
    private ClientRepository clientRepository;

    @Autowired
    private DealRepository dealRepository;

    @Override
    public List<GeneratedDocumentDto> getAllGeneratedDocuments() {
        return generatedDocumentRepository.findAll().stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public GeneratedDocumentDto getGeneratedDocumentById(Long id) {
        GeneratedDocument generatedDocument = generatedDocumentRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Generated Document not found with id: " + id));
        return convertToDto(generatedDocument);
    }

    @Override
    public List<GeneratedDocumentDto> getGeneratedDocumentsByTemplateId(Long templateId) {
        return generatedDocumentRepository.findByTemplateId(templateId).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<GeneratedDocumentDto> getGeneratedDocumentsByClientId(Long clientId) {
        return generatedDocumentRepository.findByClientId(clientId).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<GeneratedDocumentDto> getGeneratedDocumentsByDealId(Long dealId) {
        return generatedDocumentRepository.findByDealId(dealId).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<GeneratedDocumentDto> getGeneratedDocumentsByStatus(String status) {
        return generatedDocumentRepository.findByStatus(status).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public GeneratedDocumentDto createGeneratedDocument(GeneratedDocumentDto generatedDocumentDto) {
        GeneratedDocument generatedDocument = convertToEntity(generatedDocumentDto);
        GeneratedDocument savedGeneratedDocument = generatedDocumentRepository.save(generatedDocument);
        return convertToDto(savedGeneratedDocument);
    }

    @Override
    @Transactional
    public GeneratedDocumentDto updateGeneratedDocument(Long id, GeneratedDocumentDto generatedDocumentDto) {
        GeneratedDocument existingGeneratedDocument = generatedDocumentRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Generated Document not found with id: " + id));
        
        updateGeneratedDocumentFromDto(existingGeneratedDocument, generatedDocumentDto);
        
        GeneratedDocument updatedGeneratedDocument = generatedDocumentRepository.save(existingGeneratedDocument);
        return convertToDto(updatedGeneratedDocument);
    }

    @Override
    @Transactional
    public GeneratedDocumentDto updateGeneratedDocumentStatus(Long id, String status) {
        GeneratedDocument generatedDocument = generatedDocumentRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Generated Document not found with id: " + id));
        
        generatedDocument.setStatus(status);
        GeneratedDocument updatedGeneratedDocument = generatedDocumentRepository.save(generatedDocument);
        return convertToDto(updatedGeneratedDocument);
    }

    @Override
    @Transactional
    public void deleteGeneratedDocument(Long id) {
        if (!generatedDocumentRepository.existsById(id)) {
            throw new EntityNotFoundException("Generated Document not found with id: " + id);
        }
        generatedDocumentRepository.deleteById(id);
    }

    private GeneratedDocumentDto convertToDto(GeneratedDocument generatedDocument) {
        return GeneratedDocumentDto.builder()
                .id(generatedDocument.getId())
                .templateId(generatedDocument.getTemplate() != null ? generatedDocument.getTemplate().getId() : null)
                .versionId(generatedDocument.getVersion() != null ? generatedDocument.getVersion().getId() : null)
                .clientId(generatedDocument.getClient() != null ? generatedDocument.getClient().getId() : null)
                .dealId(generatedDocument.getDeal() != null ? generatedDocument.getDeal().getId() : null)
                .filePath(generatedDocument.getFilePath())
                .filledContent(generatedDocument.getFilledContent())
                .status(generatedDocument.getStatus())
                .build();
    }

    private GeneratedDocument convertToEntity(GeneratedDocumentDto generatedDocumentDto) {
        GeneratedDocument generatedDocument = new GeneratedDocument();
        generatedDocument.setId(generatedDocumentDto.getId());
        
        updateGeneratedDocumentFromDto(generatedDocument, generatedDocumentDto);
        
        return generatedDocument;
    }
    
    private void updateGeneratedDocumentFromDto(GeneratedDocument generatedDocument, GeneratedDocumentDto generatedDocumentDto) {
        if (generatedDocumentDto.getTemplateId() != null) {
            DocumentTemplate documentTemplate = documentTemplateRepository.findById(generatedDocumentDto.getTemplateId())
                    .orElseThrow(() -> new EntityNotFoundException("Document Template not found with id: " + generatedDocumentDto.getTemplateId()));
            generatedDocument.setTemplate(documentTemplate);
        }
        
        if (generatedDocumentDto.getVersionId() != null) {
            DocumentTemplateVersion documentTemplateVersion = documentTemplateVersionRepository.findById(generatedDocumentDto.getVersionId())
                    .orElseThrow(() -> new EntityNotFoundException("Document Template Version not found with id: " + generatedDocumentDto.getVersionId()));
            generatedDocument.setVersion(documentTemplateVersion);
        }
        
        if (generatedDocumentDto.getClientId() != null) {
            Client client = clientRepository.findById(generatedDocumentDto.getClientId())
                    .orElseThrow(() -> new EntityNotFoundException("Client not found with id: " + generatedDocumentDto.getClientId()));
            generatedDocument.setClient(client);
        }
        
        if (generatedDocumentDto.getDealId() != null) {
            Deal deal = dealRepository.findById(generatedDocumentDto.getDealId())
                    .orElseThrow(() -> new EntityNotFoundException("Deal not found with id: " + generatedDocumentDto.getDealId()));
            generatedDocument.setDeal(deal);
        }
        
        generatedDocument.setFilePath(generatedDocumentDto.getFilePath());
        generatedDocument.setFilledContent(generatedDocumentDto.getFilledContent());
        generatedDocument.setStatus(generatedDocumentDto.getStatus());
    }
}
